import sys
from typing import List, Optional, Literal, Dict, Annotated, Union, TypeVar, Generic, Any, Type, Callable
from pydantic import BaseModel, Field, model_validator
from enum import Enum
import operator

# All the enum classes with NONE option
class AntigenExpressionLevels(str, Enum):
    HIGH = "High Antigen Expression"
    LOW = "Low Antigen Expression"
    MODERATE = "Moderate Antigen Expression"
    NONE = "Not Specified"

class ConcentrationComponents(str, Enum):
    INTACT_ADC = "Intact ADC"
    FREE_PAYLOAD = "Free Payload"
    FREE_ANTIBODY = "Free Antibody"
    OTHER = "Other Components"

class ExperimentType(str, Enum):
    IN_VIVO = "In Vivo Studies"
    IN_VITRO = "In Vitro Studies"
    EX_VIVO = "Ex Vivo Studies"
    NONE = "NONE"
    

class ModelType(str, Enum):
    CELL_LINE = "Cell Line Model"
    CDX = "Cell Line-Derived Xenograft (CDX)"
    PDX = "Patient-Derived Xenograft (PDX)"
    ORGANOID = "Organoid Model"
    SYNGENEIC = "Syngeneic Model"
    TISSUE_SPECIMENS = "Tissue Specimens"
    TRANSGENIC = "Transgenic Model"
    NON_CELL_BASED = "Non-cell based Model"
    RODENT_MODELS = "Rodent Models"
    NON_HUMAN_PRIMATES = "Non-Human Primate Models"
    NONE = "NONE"
   

class LinkerType(str, Enum):
    CLEAVABLE = "Cleavable Linker"
    NON_CLEAVABLE = "Non-cleavable Linker"
    NONE = "NONE"
    

class AntibodyClonality(str, Enum):
    MONOCLONAL = "Monoclonal Antibody (mAb)"
    POLYCLONAL = "Polyclonal Antibody (pAb)"
    NONE = "NONE"     
    

class AntibodySpecies(str, Enum):
    MURINE = "Murine"
    CHIMERIC = "Chimeric"
    HUMANIZED = "Humanized"
    NONE = "NONE"     
    
class AntibodyIsotype(str, Enum):
    IGG = "IgG"
    IGM = "IgM"
    IGA = "IgA"
    IGE = "IgE"
    IGD = "IgD"
    NONE = "NONE"     
   

class EndpointType(str, Enum):
    SAFETY = "safety"
    EFFICACY = "efficacy"
    PHARMACOKINETICS = "pharmacokinetics"
    PHARMACODYNAMICS = "pharmacodynamics"
    BIOMARKER = "biomarker"
    NONE = "NONE"     

class AntibodyDrugConjugateType(str, Enum):
    INVESTIGATIVE = "Investigative"
    REFERENCE = "Reference"

class EndpointName(BaseModel):
    # Endpoints ----------------
    ANTIGEN_EXPRESSION: bool = Field(..., description="Boolean value indicating The level of antigen expression in the target cells, categorized as high or low. This categorization can be directly mentioned in the article or determined based on percentage thresholds, where more than 75% expression is considered high and less than 25% is considered low or any other calculations. Look for quantification methods such as Flow Cytometry or Western Blot.")
    ANTIGEN_EXPRESSION_H_SCORE: bool = Field(..., description="Boolean value indicating Quantitative measurement of antigen expression using H-score for ADC target validation. It combines the intensity of staining and the percentage of positive cells to provide a comprehensive score ranging from 0 to 300."
    " Look for quantification methods such as Immunohistochemistry (IHC) or Immunocytochemistry (ICC).")
    EC50_HALF_MAXIMAL_EFFECTIVE_CONCENTRATION: bool = Field(..., description="Boolean value indicating The concentration of the drug that produces a response halfway between the baseline and maximum achievable response."
    "This is often determined by dose-response curve analysis."
    "Look for quantification methods such as non-cell-based dose-response assays (e.g., ELISA) or cell-based dose-response studies.")
    TUMOR_GROWTH_INHIBITION: bool = Field(..., description="Boolean value indicating The measure of the effectiveness of a treatment in inhibiting the growth of tumors."
    "TGI is typically expressed as a percentage, indicating the reduction in tumor size or growth rate compared to a control group."
    "Look for quantification methods such as tumor volume measurements (e.g., caliper measurements, bioluminescence imaging, or MRI)")
    CMAX_MAXIMUM_CONCENTRATION: bool = Field(..., description="Boolean value indicating The highest concentration of the ADC (antibody + payload) in the blood after it is administered."
    "Look for quantification methods such as serum or plasma concentration assays (e.g., ELISA) ")
    HALF_LIFE_PERIOD: bool = Field(..., description="Boolean value indicating The time it takes for the concentration of the ADC to reduce to half of its initial value after administration."
    "Look for quantification methods such as serum or plasma concentration assays (e.g., ELISA) ")
    INTERNALIZATION: bool = Field(..., description="Boolean value indicating This metric quantifies the percentage of the total administered antibody-drug conjugate (ADC) that is successfully taken up by target cells within a specified time frame."
    "It is calculated by comparing the amount of ADC internalized by the cells to the total amount of ADC initially available for binding and uptake."
    "Look for quantification methods such as flow cytometry, Confocal Microscopy, or Fluorescence Microscopy")
    ANTI_TUMOR_ACTIVITY_DOSE: bool = Field(..., description="Boolean value indicating The amount of ADC or drug administered to the experimental model for anti-tumor activity assessment.")

    TOXICITY_FREQUENCY: bool = Field(..., description="Boolean value indicating The frequency of administration of the ADC for toxicity evaluation (e.g., once daily, twice weekly, etc.) in the experimental model.")

    LETHAL_DOSE: bool = Field(..., description="Boolean value indicating The amount of ADC that causes death in a specified percentage of the experimental models"
    "(e.g., True:LD50, the dose lethal to 50% of the population)."
    "Look for quantification methods such as Dose-Mortality Response")
    
    
class Endpoint(BaseModel):
    """Base class for all endpoints"""
    measured_value: str = Field(None, description="The value of the endpoint")
    measured_time: str = Field(None, description="The timepoint at which the endpoint is measured")
    measured_concentration: str = Field(None, description="The concentration of the ADC at the timepoint of measurement")

# Refactored Pydantic Models

# ADC
class AntibodyDrugConjugate(BaseModel):
    """Information about ADC: Antibody Drug Conjugate with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    adc_name: str = Field(..., description="The name of the Antibody Drug Conjugate")
    adc_type: AntibodyDrugConjugateType = Field(..., description="The type of the above ADC, whether it is an investigative or reference ADC. Investigative ADC is the one that is being tested in the preclinical experiment, while reference ADC is the one that is already approved and used as a benchmark for comparison or which is just mentioned in the study. Most importantly, reference ADC is not experimented on in the preclinical study.")
    #Antibody fields
    antibody_name: str = Field(..., description="The name of the antibody used in the above ADC")
    antibody_clonality: AntibodyClonality = Field(..., description="The type of clonality for the above antibody")
    antibody_species: AntibodySpecies = Field(..., description="The species origin of the above antibody")
    antibody_isotype: AntibodyIsotype = Field(..., description="The isotype classification of the above antibody")
    #Payload fields
    payload_name: str = Field(..., description="The name of the chemical compound used as payload to be delivered using above ADC")    
    payload_target: str = Field(..., description="The specific molecular target or pathway that the above payload is designed to affect")
    #Linker fields
    linker_name: str = Field(..., description="The name of the chemical compound used as a linker to bind antibody and payload together in the above ADC")
    linker_type: LinkerType = Field(..., description="The cleavable property of the above linker")
    #Antigen fields
    antigen_name: str = Field(..., description="The name of the antigen targeted by the above ADC")

# TODO - Edit adc agent prompt - to call load_adc_names_into_memory Tool to extract and save the ADC names in memory

class ExperimentalModel(BaseModel):
    """Information about experimental model used in the preclinical experiment for given ADC with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    model_name: str = Field(..., description="The name of the model used in the experiment for given ADC. This is cell line name or tissue specimen name or the organism name Only! DO NOT add the model type (e.g. CDX, PDX, cell line, Xenograft etc) in this field since the same model culd be used as cell line, xenograft, tissue etc!! Just keep the name as an identifier here!")
    cancer_type: str = Field(..., description="A cancer type refers to the broad, primary classification of cancer, based on the organ or tissue where the cancer originates. It is essentially the location in the body where the cancerous cells began to form.")
    cancer_subtype: Optional[str] = Field(None, description="A cancer subtype is a further classification within a cancer type, based on specific histological, genetic, or molecular characteristics of the tumor cells. Subtypes can influence the cancer's behavior, its response to treatment, and its prognosis.")

class Experiment(BaseModel):
    """Information about the experiment conducted on the experimental model for given ADC """
    experiment_type: ExperimentType = Field(..., description="The type of experiment conducted on this model for given ADC")
    model_type: ModelType = Field(..., description="The type of model used in the experiment for given ADC")

    @model_validator(mode='after')
    def validate_experiment_model_consistency(self):
        """Validate logical consistency between experiment_type and model_type."""
        experiment_type = self.experiment_type
        model_type = self.model_type

        # Define valid combinations using enum values
        valid_combinations = {
            ExperimentType.IN_VITRO: {ModelType.CELL_LINE, ModelType.NON_CELL_BASED, ModelType.ORGANOID, ModelType.NONE},
            ExperimentType.EX_VIVO: {ModelType.TISSUE_SPECIMENS, ModelType.NONE},
            ExperimentType.IN_VIVO: {
                ModelType.CDX, ModelType.PDX, ModelType.SYNGENEIC,
                ModelType.TRANSGENIC, ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE
            },
            ExperimentType.NONE: {
                ModelType.CELL_LINE, ModelType.NON_CELL_BASED, ModelType.ORGANOID, ModelType.TISSUE_SPECIMENS,
                ModelType.CDX, ModelType.PDX, ModelType.SYNGENEIC, ModelType.TRANSGENIC,
                ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE
            }
        }

        if model_type not in valid_combinations[experiment_type]:
            valid_options = [option.value for option in valid_combinations[experiment_type]]
            raise ValueError(
                f"Invalid combination: {experiment_type.value} experiments cannot use '{model_type.value}' models. Recheck your answer!"
            )

        return self

# Specialized endpoint models named exactly after the EndpointName enum values
class AntigenExpression(Experiment):
    """Endpoint Information about Antigen Expression for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: AntigenExpressionLevels = Field(None, description="Categorization of the level of antigen expression in the target cells, categorized as high, low, or moderate. If the categorization is not explicitly mentioned in the article, then it can be interpreted based on percentage of antigen expression. More than 75% expression is considered high, less than 25% is considered low, and between 25% and 75% is considered moderate.")

    @model_validator(mode='after')
    def validate_model_type_for_antigen_expression(self):
        """Validate that model_type is compatible with AntigenExpression endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.ORGANOID, ModelType.TISSUE_SPECIMENS, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for AntigenExpression: {self.model_type.value}. Antigen Expression cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

    
class AntigenExpressionHScore(Experiment):
    """Endpoint measurement of antigen expression using H-score for ADC target validation with supporting citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted")
    measured_value: str = Field(None, description="H-score value (0-300) representing semi-quantitative immunohistochemistry assessment of antigen expression intensity and distribution")

    @model_validator(mode='after')
    def validate_model_type_for_antigen_expression_h_score(self):
        """Validate that model_type is compatible with AntigenExpressionHScore endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.ORGANOID, ModelType.TISSUE_SPECIMENS, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for AntigenExpressionHScore: {self.model_type.value}. H Score cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self



class EC50_HalfMaximalEffectiveConcentration(Experiment):
    """Endpoint Information about a unique measurement of Half Maximal Effective Concentration (EC50) for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted for a single measurement of EC50")
    measured_value: str = Field(None, description="The concentration of the drug that produces a response halfway between the baseline and maximum achievable response. This is often determined by dose-response curve analysis and quantified using methods such as Non-cell based Dose-response(ELISA), Cell-based Dose-response. This value is typically expressed in nanomolar (nM) or micromolar (μM) units.")
    measured_time: str = Field(None, description="The specific time period after ADC administration that the EC50 value is measured.")

    @model_validator(mode='after')
    def validate_model_type_for_ec50(self):
        """Validate that model_type is compatible with EC50_HalfMaximalEffectiveConcentration endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.NON_CELL_BASED, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for EC50_HalfMaximalEffectiveConcentration: {self.model_type.value}. EC50 Score cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class TumorGrowthInhibition(Experiment):
    """Endpoint Information about a unique measurement of Tumor Growth Inhibition (TGI) for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted for a single measurement of TGI")
    measured_value: str = Field(None, description="The measure of the effectiveness of a treatment in inhibiting the growth of tumors. TGI is typically expressed as a percentage, indicating the reduction in tumor size or growth rate compared to a control group. It is usually quantified using methods such as Caliper measurement or Imaging techniques")
    measured_time: str = Field(None, description="The specific time period after ADC administration at which tumor volume is measure for assessing inhibition of tumor growth. This is typically expressed in days or weeks or months")
    measured_concentration: str = Field(None, description="The concentration of the ADC in the experimental model at the time of measurement. This is typically expressed in nanomolar (nM) or micromolar (μM) units.")

    @model_validator(mode='after')
    def validate_model_type_for_tumor_growth_inhibition(self):
        """Validate that model_type is compatible with TumorGrowthInhibition endpoint."""
        allowed_model_types = {ModelType.CDX, ModelType.PDX, ModelType.SYNGENEIC, ModelType.TRANSGENIC, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for TumorGrowthInhibition: {self.model_type.value}. Tumor Growth Inhibition is measured specifically for in vivo cancer carrying models! It cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class Cmax_MaximumConcentration(Experiment):
    """Endpoint Information about a unique measurement of Maximum Concentration (Cmax) for the given ADC tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted for a single measurement of Cmax")
    measured_value: str = Field(None, description="The highest concentration of the ADC in the blood after it has been administered. It is usually quantified using methods such as post-dose Serum sample analysis using appropriate techniques (e.g., ELISA) to determine the above component's concentration. This value is typically expressed in nanograms per milliliter (ng/mL) or micrograms per milliliter (μg/mL).")
    measured_time: str = Field(None, description="The specific time point/ time duration at which the peak plasma concentration (Cmax) of the above ADC is observed after administration. This is typically expressed in hours or days post-administration.")
    measured_concentration: str = Field(None, description="The dose of ADC administered to the experimental model, after which the maximum serum concentration (Cmax) is achieved. This is typically expressed in milligrams per kilogram (mg/kg) or nanograms per milliliter (ng/mL).")

    @model_validator(mode='after')
    def validate_model_type_for_cmax(self):
        """Validate that model_type is compatible with Cmax_MaximumConcentration endpoint."""
        allowed_model_types = {
            ModelType.TISSUE_SPECIMENS, ModelType.CDX, ModelType.PDX, ModelType.SYNGENEIC,
            ModelType.TRANSGENIC, ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE
        }
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for Cmax_MaximumConcentration: {self.model_type.value}. Cmax Maximal ADC Concentration cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class HalfLifePeriod(Experiment):
    """Endpoint Information about a unique measurement of Half Life Period for the given ADC component tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted for a single measurement of Half Life Period")
    measured_value: str = Field(None, description="The time it takes for the concentration of the given ADC to reduce to half of its initial value after administration. This is usually quantified using methods such as post-dose Serum sample analysis using appropriate techniques (e.g., ELISA) to determine the above component's concentration. This value is typically expressed in hours or days.")
    measured_concentration: str = Field(None, description="The dose of ADC administered to the experimental model, after which the elimination half-life (t1/2) is observed. This is typically expressed in milligrams per kilogram (mg/kg) or nanograms per milliliter (ng/mL).")

    @model_validator(mode='after')
    def validate_model_type_for_half_life(self):
        """Validate that model_type is compatible with HalfLifePeriod endpoint."""
        allowed_model_types = {
            ModelType.CDX, ModelType.PDX, ModelType.SYNGENEIC, ModelType.TRANSGENIC,
            ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE
        }
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for HalfLifePeriod: {self.model_type.value}. ADC Half Life is measured for in vivo models! It cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class Internalization(Experiment):
    """Endpoint Information about a unique measurement of ADC internalization for the given ADC component tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted or interpreted for a single measurement of internalization")
    measured_value: str = Field(None, description="The percentage or amount of the administered ADC component that is internalized by target cells within a specified time frame. This is typically determined by comparing the amount detected to the total amount administered, using methods such as flow cytometry, confocal microscopy, or fluorescence microscopy. This value is usually expressed as a percentage or absolute amount.")
    measured_concentration: str = Field(None, description="The concentration or dose of ADC administered to the experimental model after which internalization is measured. This is typically expressed in nanomolar (nM), micrograms per milliliter (μg/mL), or milligrams per kilogram (mg/kg).")
    # Time elapsed since administration and capture of internalization
    measured_timepoint: str = Field(None, description="The time after ADC exposure when internalization was assessed, typically expressed in hours or days")

    @model_validator(mode='after')
    def validate_model_type_for_internalization(self):
        """Validate that model_type is compatible with Internalization endpoint."""
        allowed_model_types = {ModelType.CELL_LINE, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for Internalization: {self.model_type.value}. ADC Internalization cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class AntiTumorActivityDose(Experiment):
    """Endpoint Information about a unique measurement of effective ADC doses tested on the experimental model with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted for a single measurement of effective dose")
    measured_value: str = Field(None, description="Single measurement of dose that is reported to be effective in anti tumor activity.")

    @model_validator(mode='after')
    def validate_model_type_for_anti_tumor_activity_dose(self):
        """Validate that model_type is compatible with AntiTumorActivityDose endpoint."""
        allowed_model_types = {ModelType.CDX, ModelType.PDX, ModelType.SYNGENEIC, ModelType.TRANSGENIC, ModelType.NONE}
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for AntiTumorActivityDose: {self.model_type.value}. Anti Tumor Dose must be for in vivo animal models. It cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self


class ToxicityFrequency(Experiment):
    """Endpoint Information about a unique measurement of toxicity frequency tested on the experimental model for given ADC with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted for a single measurement of toxicity frequency")
    measured_value: str = Field(None, description="The number of times the dose of ADC is administered to the experimental model within a specific time period (e.g., daily, weekly, etc.) for toxicity evaluation. This is typically expressed as a frequency count (e.g., 1x, 2x, 3x per week) or as a total number of administrations over the study period.")
    measured_concentration: str = Field(None, description="The doses of drug (ADC) delivered to a subject per administration, possibly following a defined dosing schedule (frequency). This is typically expressed in milligrams per kilogram (mg/kg) or nanograms per milliliter (ng/mL).")

    @model_validator(mode='after')
    def validate_model_type_for_toxicity_frequency(self):
        """Validate that model_type is compatible with ToxicityFrequency endpoint."""
        allowed_model_types = {
            ModelType.CDX, ModelType.PDX, ModelType.SYNGENEIC, ModelType.TRANSGENIC,
            ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE
        }
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for ToxicityFrequency: {self.model_type.value}. Toxicity frequency is measured for in vivo animal models. It cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

class LethalDose(Experiment):
    """Endpoint Information about a unique measurement of lethal dose tested on the experimental model for given ADC with citations"""
    citations: List[str] = Field(..., description="List of sentences from research paper where the below attributes information is extracted / interpreted for a single measurement of lethal dose")
    measured_value: str = Field(None, description="The amount of ADC that is shown to cause death in a specific percentage of experimental models (e.g., LD50, the dose lethal to 50% of the population). This is typically determined through dose-mortality response studies and is expressed in milligrams per kilogram (mg/kg) or nanograms per milliliter (ng/mL).")
    measured_death_percentage: str = Field(None, description="The percentage of experimental models that succumbed to the lethal dose of ADC. This is typically expressed as a percentage (e.g., 50% for LD50).")

    @model_validator(mode='after')
    def validate_model_type_for_lethal_dose(self):
        """Validate that model_type is compatible with LethalDose endpoint."""
        allowed_model_types = {
            ModelType.CDX, ModelType.PDX, ModelType.SYNGENEIC, ModelType.TRANSGENIC,
            ModelType.RODENT_MODELS, ModelType.NON_HUMAN_PRIMATES, ModelType.NONE
        }
        if self.model_type not in allowed_model_types:
            allowed_values = [mt.value for mt in allowed_model_types]
            raise ValueError(
                f"Invalid model type for LethalDose: {self.model_type.value}. Lethal Dose is measured for in vivo animal models. It cannot be calculated for specified model type. Maybe the model type or experiment type you are reporting is incorrect! Recheck your answer!"
            )
        return self

# Function to get the appropriate endpoint model class based on the endpoint name
def get_endpoint_model(endpoint_name: EndpointName) -> Type[Endpoint]:
    """Get the appropriate endpoint model class based on the endpoint name"""
    # Convert enum name to class name format (remove underscores and capitalize each word)
    # For example: ANTIGEN_EXPRESSION -> AntigenExpression
    class_name = ''.join(word.capitalize() for word in endpoint_name.split('_'))

    if class_name == 'Ec50HalfMaximalEffectiveConcentration':
        class_name = 'EC50_HalfMaximalEffectiveConcentration'
    
    if class_name == 'CmaxMaximumConcentration':
        class_name = 'Cmax_MaximumConcentration'
    
    # Get the module where this function is defined
    module = sys.modules[__name__]
    
    # Try to get the class by name from the module
    try:
        return getattr(module, class_name)
    except AttributeError:
        # If the class doesn't exist, return the base Endpoint class
        return Endpoint
