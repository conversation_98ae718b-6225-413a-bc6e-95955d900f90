from mcp import ClientSession, StdioServerParameters, types
from mcp.client.stdio import stdio_client
import asyncio

# Create server parameters for stdio connection
server_params = StdioServerParameters(
    command="python",  # Executable
    args=["src/mondo_standardizer.py"],  # Optional command line arguments
    env=None,  # Optional environment variables
)
 
async def run():
    async with stdio_client(server_params) as (read, write):
        async with ClientSession(
            read, write
        ) as session:
           # Initialize the connection
            await session.initialize()
 
            # List available prompts
            prompts = await session.list_prompts()
 
            # List available resources
            resources = await session.list_resources()
 
            # List available tools
            tools = await session.list_tools()
            print(tools)

            return prompts, resources, tools
 
if __name__ == "__main__":
    
    print(asyncio.run(run()))
    asyncio.run(run())