import pandas as pd
import os

# Define the path to the parquet file
file_path = os.path.join('parquet', 'dry_run_xmls.parquet')

# Read the parquet file
df = pd.read_parquet(file_path)

# View the first few rows of the dataframe
print(df.head())

# Get basic information about the dataframe
print(df.info())

# Get summary statistics
print(df.describe())


# Create xml_files directory if it doesn't exist
xml_folder = 'xml_files'
os.makedirs(xml_folder, exist_ok=True)

# Loop through each row and save the XML content to a separate file
for index, row in df.iterrows():
    # Use the oa_works_id as the filename
    file_name = f"{row['oa_works_id']}.xml"
    file_path = os.path.join(xml_folder, file_name)
    
    # Write the XML content to the file
    with open(file_path, 'w', encoding='utf-8') as file:
        file.write(row['rawContent'])
    
    print(f"Saved {file_name}")

print(f"\nAll {len(df)} XML files have been saved to the '{xml_folder}' folder.")